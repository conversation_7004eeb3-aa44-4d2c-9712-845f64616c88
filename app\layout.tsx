import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"
import { Inter } from "next/font/google"
import { Toaster } from "@/components/ui/toaster"
import "./globals.css"
import { ThemeProvider } from "@/components/theme-provider"
import { TonWalletProvider } from "@/components/ton-wallet-provider"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "Ton Store - Digital Assets Marketplace",
  description: "Buy, sell, and auction digital assets on the TON blockchain",
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
          <TonWalletProvider>
            {children}
          </TonWalletProvider>
          <Toaster />
        </ThemeProvider>
      </body>
    </html>
  )
}