# tonstoremarketplace

This is a boilerplate to build a decentralized application on the TON blockchain.

This project is a boilerplate for a decentralized application (dApp) on the TON blockchain. It includes a frontend built with Next.js and a backend built with Lara<PERSON>. The project is designed to be easily deployable on Vercel.

## Features

- **Frontend**: Built with Next.js, providing a fast and modern user experience.
- **Backend**: Uses a mock API for local development and testing.
- **Deployment**: Ready for Vercel deployment.

## Getting Started

1. Clone the repository

```bash
git clone https://github.com/ton-connect/dapp-boilerplate.git
cd dapp-boilerplate
```

2. Install Dependencies

```bash
pnpm install
```

3. Run the Development Server

```bash
pnpm run dev
```
Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## Learn More

To learn more about the TON ecosystem and dApp development, check out the following resources:

- [TON Documentation](https://ton.org/docs)
- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/deployment) for more details.
# tonstoremarketspot
#   t o n s t o r e d i g i t a l m a r k e t 
 
 #   s t o r e o n t o n 
 
 